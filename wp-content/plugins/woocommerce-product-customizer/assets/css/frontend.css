/* WooCommerce Product Customizer Frontend Styles */

.wpc-product-customization {
    margin: 15px 0;
    padding: 0;
    border: none;
    background: transparent;
}

/* TESTE: Estilos para campo de upload local */
.wpc-image-field-test {
    position: relative;
    margin-top: 15px;
    padding: 15px;
    border: 2px dashed #ff6b35;
    border-radius: 8px;
    background: #fff9f7;
}

.wpc-test-label {
    margin-bottom: 10px;
    color: #ff6b35;
    font-weight: 600;
}

.wpc-upload-area-test {
    position: relative;
    min-height: 60px;
    padding: 15px;
    border: 2px dashed #ff6b35;
    border-radius: 6px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.wpc-upload-area-test:hover {
    border-color: #e55a2b;
    background: #fff5f2;
}

.wpc-upload-area-test.wpc-dragover {
    border-color: #e55a2b;
    background: #fff0eb;
    transform: scale(1.02);
}

.wpc-upload-instructions-test {
    margin-top: 8px;
    text-align: center;
}

.wpc-upload-instructions-test .wpc-upload-text {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: #ff6b35;
    font-weight: 500;
}

.wpc-upload-instructions-test .wpc-upload-requirements {
    margin: 0;
    font-size: 11px;
    color: #999;
}

.wpc-image-preview-test {
    margin-top: 15px;
    padding: 10px;
    background: #fff;
    border: 1px solid #ff6b35;
    border-radius: 6px;
}

.wpc-thumbnail-test {
    max-width: 100px;
    height: auto;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.wpc-remove-image-test {
    background: #ff6b35;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -8px;
    right: -8px;
}

.wpc-remove-image-test:hover {
    background: #e55a2b;
}

.wpc-preview-btn-test {
    margin-top: 10px;
    padding: 8px 12px;
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.wpc-preview-btn-test:hover {
    background: #e55a2b;
}

.wpc-filename-test,
.wpc-filesize-test {
    font-size: 11px;
    color: #666;
    display: block;
}
/* FIM TESTE localStorage */

/* TESTE: Estilos para campo de upload com cache do servidor */
.wpc-image-field-cache {
    position: relative;
    margin-top: 15px;
    padding: 15px;
    border: 2px dashed #2196F3;
    border-radius: 8px;
    background: #f3f9ff;
}

.wpc-cache-label {
    margin-bottom: 10px;
    color: #2196F3;
    font-weight: 600;
}

.wpc-upload-area-cache {
    position: relative;
    min-height: 60px;
    padding: 15px;
    border: 2px dashed #2196F3;
    border-radius: 6px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.wpc-upload-area-cache:hover {
    border-color: #1976D2;
    background: #f0f7ff;
}

.wpc-upload-area-cache.wpc-dragover {
    border-color: #1976D2;
    background: #e3f2fd;
    transform: scale(1.02);
}

.wpc-upload-instructions-cache {
    margin-top: 8px;
    text-align: center;
}

.wpc-upload-instructions-cache .wpc-upload-text {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: #2196F3;
    font-weight: 500;
}

.wpc-upload-instructions-cache .wpc-upload-requirements {
    margin: 0;
    font-size: 11px;
    color: #999;
}

.wpc-image-preview-cache {
    margin-top: 15px;
    padding: 10px;
    background: #fff;
    border: 1px solid #2196F3;
    border-radius: 6px;
}

.wpc-thumbnail-cache {
    max-width: 100px;
    height: auto;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.wpc-remove-image-cache {
    background: #2196F3;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -8px;
    right: -8px;
}

.wpc-remove-image-cache:hover {
    background: #1976D2;
}

.wpc-preview-btn-cache {
    margin-top: 10px;
    padding: 8px 12px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.wpc-preview-btn-cache:hover {
    background: #1976D2;
}

.wpc-filename-cache,
.wpc-filesize-cache,
.wpc-cache-info {
    font-size: 11px;
    color: #666;
    display: block;
}

.wpc-cache-info {
    color: #2196F3;
    font-weight: 500;
}
/* FIM TESTE Cache Servidor */



.wpc-required {
    color: #d63638;
    font-weight: bold;
}

.wpc-price {
    color: #0073aa;
    font-size: 16px;
    font-weight: 500;
}

.wpc-instructions {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.wpc-instructions p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

.wpc-customization-group {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #ddd;
}

.wpc-field-wrapper {
    margin-bottom: 20px;
}

.wpc-field-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.wpc-field-info {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

/* Text Fields */
.wpc-text-field {
    width: 100%;
    min-height: 60px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.wpc-text-field:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.wpc-char-counter {
    text-align: right;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.wpc-char-counter.wpc-warning {
    color: #dba617;
}

.wpc-char-counter.wpc-error {
    color: #d63638;
}

/* Image Upload Fields */
.wpc-image-field {
    position: relative;
}

.wpc-upload-area {
    position: relative;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 120px;
    max-height: 120px;
    width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

.wpc-upload-area:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

/* Remove as classes has-image que não são mais necessárias */

.wpc-upload-area.wpc-dragover {
    border-color: #0073aa;
    background: #e6f3ff;
    transform: scale(1.02);
}

.wpc-upload-content {
    pointer-events: none;
}

.wpc-upload-icon {
    margin-bottom: 8px;
    color: #666;
}

.wpc-upload-icon svg {
    width: 32px;
    height: 32px;
}

/* Upload Instructions - moved outside upload area */
.wpc-upload-instructions {
    margin-top: 8px;
    text-align: left;
}

.wpc-upload-text {
    margin: 0 0 4px 0;
    font-size: 13px;
    font-weight: 500;
    color: #333;
    line-height: 1.3;
}

.wpc-upload-requirements {
    margin: 0;
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

.wpc-file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Image Preview */
.wpc-image-preview {
    margin-top: 10px;
    max-width: 100%;
    overflow: hidden;
}

.wpc-image-container {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wpc-thumbnail {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: 6px !important;
    transition: transform 0.2s ease !important;
    display: block !important;
    max-width: none !important;
    max-height: none !important;
}

.wpc-thumbnail:hover {
    transform: scale(1.05) !important;
}

.wpc-image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.wpc-image-container:hover .wpc-image-actions {
    opacity: 1;
}

.wpc-remove-image {
    background: rgba(214, 54, 56, 0.9) !important;
    color: #fff !important;
    border: none !important;
    border-radius: 50% !important;
    width: 28px !important;
    height: 28px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
    font-size: 14px !important;
    font-weight: bold !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1 !important;
    text-decoration: none !important;
    outline: none !important;
}

.wpc-remove-image:hover {
    background: rgba(179, 45, 46, 0.95) !important;
    transform: scale(1.1) !important;
    color: #fff !important;
}

.wpc-remove-image svg {
    width: 16px !important;
    height: 16px !important;
    stroke: currentColor !important;
    fill: none !important;
    pointer-events: none !important;
}

.wpc-image-info {
    margin-top: 8px;
    font-size: 11px;
    color: #666;
    max-width: 120px;
    word-wrap: break-word;
}

.wpc-filename {
    display: block;
    font-weight: 500;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.wpc-filesize {
    color: #999;
    font-size: 10px;
}

/* Upload Progress */
.wpc-upload-progress {
    margin-top: 10px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    height: 6px;
}

.wpc-upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
}

.wpc-upload-status {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* Validation Messages */
.wpc-validation-messages {
    margin-top: 15px;
}

.wpc-error-message {
    padding: 10px 15px;
    background: #ffeaea;
    border: 1px solid #d63638;
    border-radius: 4px;
    color: #d63638;
    font-size: 14px;
    margin-bottom: 10px;
}

.wpc-success-message {
    padding: 10px 15px;
    background: #eafaf1;
    border: 1px solid #00a32a;
    border-radius: 4px;
    color: #00a32a;
    font-size: 14px;
    margin-bottom: 10px;
}

/* Loading States */
.wpc-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wpc-loading .wpc-upload-area {
    background: #f0f0f0;
    border-color: #ccc;
}

.wpc-loading .wpc-upload-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid #ccc;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wpc-spin 1s linear infinite;
}

@keyframes wpc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .wpc-product-customization {
        margin: 15px 0;
        padding: 15px;
    }
    

    
    .wpc-customization-group {
        padding: 15px;
    }
    
    .wpc-upload-area {
        padding: 15px 10px;
        max-width: 150px;
    }

    .wpc-upload-icon svg {
        width: 36px;
        height: 36px;
    }
    
    .wpc-upload-text {
        font-size: 14px;
    }
    
    .wpc-image-container {
        max-width: 150px;
    }
}

@media screen and (max-width: 480px) {
    .wpc-product-customization {
        margin: 10px 0;
        padding: 10px;
    }
    
    .wpc-customization-group {
        padding: 10px;
    }
    
    .wpc-upload-area {
        padding: 12px 8px;
        max-width: 100px;
    }
    
    .wpc-text-field {
        min-height: 60px;
        padding: 10px;
    }
}

/* Preview Button */
.wpc-preview-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,115,170,0.3);
}

.wpc-preview-btn:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,115,170,0.4);
}

.wpc-preview-btn svg {
    flex-shrink: 0;
}

/* Modal Styles */
.wpc-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wpc-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

.wpc-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: wpc-modal-appear 0.3s ease-out;
}

@keyframes wpc-modal-appear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.wpc-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.wpc-modal-header h3 {
    margin: 0;
    color: #0073aa;
    font-size: 20px;
    font-weight: 600;
}

.wpc-modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    color: #666;
}

.wpc-modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.wpc-modal-body {
    padding: 24px;
    text-align: center;
}

.wpc-preview-canvas-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    overflow: hidden;
}

#wpc-preview-canvas {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.wpc-preview-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #666;
    font-size: 14px;
}

.wpc-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: wpc-spin 1s linear infinite;
}

@keyframes wpc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.wpc-modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
    text-align: center;
}

.wpc-preview-note {
    margin: 0 0 15px 0;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

.wpc-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.wpc-btn-primary {
    background: #0073aa;
    color: white;
}

.wpc-btn-primary:hover {
    background: #005a87;
}

/* Modal responsive */
@media (max-width: 768px) {
    .wpc-modal-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: 10px;
    }

    .wpc-modal-header,
    .wpc-modal-body,
    .wpc-modal-footer {
        padding: 16px;
    }

    .wpc-modal-header h3 {
        font-size: 18px;
    }

    .wpc-preview-canvas-wrapper {
        min-height: 300px;
    }

    #wpc-preview-canvas {
        max-width: 100%;
    }

    .wpc-preview-btn {
        font-size: 13px;
        padding: 6px 12px;
    }
}

/* Restoration Notification */
.wpc-restoration-notice {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    background: linear-gradient(135deg, #00a32a 0%, #008a20 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 163, 42, 0.3);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 350px;
}

.wpc-restoration-notice.wpc-notice-show {
    transform: translateX(0);
    opacity: 1;
}

.wpc-notice-content {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.wpc-notice-content svg {
    flex-shrink: 0;
}

@media (max-width: 768px) {
    .wpc-restoration-notice {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .wpc-restoration-notice.wpc-notice-show {
        transform: translateY(0);
    }
}
