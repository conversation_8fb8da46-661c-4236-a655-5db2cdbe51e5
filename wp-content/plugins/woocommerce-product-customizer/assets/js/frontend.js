jQuery(document).ready(function($) {

    // Initialize customization fields
    initCustomizationFields();

    function initCustomizationFields() {
        // Initialize text fields
        initTextFields();

        // Initialize image upload fields
        initImageFields();

        // Validate form before add to cart
        validateFormOnSubmit();

        // Initialize add to cart button control
        initAddToCartControl();

        // Initialize image persistence system
        initImagePersistence();

        // Initial validation check
        validateAllFields();

        // Initialize cart restoration
        initCartRestoration();

        // Handle cart restoration parameter
        handleCartRestoration();

        // Cart link modification is now handled by PHP script injection

        // Disable HTML5 validation on WooCommerce forms to prevent conflicts
        $('form.cart, form.variations_form').attr('novalidate', 'novalidate');
    }
    
    // Text Fields Functionality
    function initTextFields() {
        $('.wpc-text-field').each(function() {
            const field = $(this);
            const counter = field.siblings('.wpc-char-counter');
            const maxLength = parseInt(field.attr('maxlength'));
            
            // Update character counter
            field.on('input', function() {
                const currentLength = $(this).val().length;
                const current = counter.find('.wpc-current');
                
                current.text(currentLength);
                
                // Update counter styling
                counter.removeClass('wpc-warning wpc-error');
                if (currentLength > maxLength * 0.9) {
                    counter.addClass('wpc-warning');
                }
                if (currentLength >= maxLength) {
                    counter.addClass('wpc-error');
                }
                
                // Validate field
                validateTextField(field);

                // Check all fields after change
                validateAllFields();
            });

            // Initial validation
            validateTextField(field);
        });
    }
    
    // Image Fields Functionality
    function initImageFields() {
        $('.wpc-image-field').each(function() {
            const imageField = $(this);
            const uploadArea = imageField.find('.wpc-upload-area');
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');
            const maxSize = parseFloat(imageField.data('max-size'));
            
            // Click to upload
            uploadArea.on('click', function() {
                fileInput.click();
            });
            
            // File input change
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    handleFileUpload(file, imageField, maxSize);
                }
            });
            
            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('wpc-dragover');
            });
            
            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
            });
            
            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0], imageField, maxSize);
                }
            });
            
            // Remove image functionality
            imageField.on('click', '.wpc-remove-image', function(e) {
                e.preventDefault();
                removeImage(imageField);

                // Check all fields after removal
                validateAllFields();
            });
        });
    }
    
    // Handle file upload
    function handleFileUpload(file, imageField, maxSize) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            showError(imageField, wpc_frontend.strings.invalid_format);
            return;
        }
        
        // Validate file size
        const maxSizeBytes = maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            showError(imageField, wpc_frontend.strings.file_too_large + ' ' + maxSize + ' MB');
            return;
        }
        
        // Show loading state
        imageField.addClass('wpc-loading');
        showUploadProgress(imageField, 0);
        
        // Create FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('field_id', getFieldId(imageField));
        formData.append('max_size', maxSize);
        formData.append('action', 'wpc_upload_file');
        formData.append('nonce', wpc_frontend.nonce);
        
        // Upload file via AJAX
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,

            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        showUploadProgress(imageField, percentComplete);
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                if (response.success) {
                    showImagePreview(imageField, response.data);
                    clearError(imageField);

                    // Trigger image uploaded event for persistence
                    const fieldId = imageField.find('input[type="file"]').attr('name').replace('wpc_field_', '');
                    $(document).trigger('wpc_image_uploaded', [fieldId, response.data]);

                    // Check all fields after successful upload
                    validateAllFields();
                } else {
                    showError(imageField, response.data.message);

                    // Check all fields after failed upload
                    validateAllFields();
                }
            },
            error: function(xhr, status, error) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                let errorMessage = wpc_frontend.strings.upload_error;
                if (xhr.status === 400) {
                    errorMessage = 'Erro 400: Requisição inválida. Verifique o arquivo selecionado.';
                } else if (xhr.status === 413) {
                    errorMessage = 'Erro 413: Arquivo muito grande para o servidor.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Erro 500: Erro interno do servidor.';
                }

                showError(imageField, errorMessage);

                // Check all fields after error
                validateAllFields();
            }
        });
    }
    
    // Show image preview
    function showImagePreview(imageField, fileData) {
        const preview = imageField.find('.wpc-image-preview');
        const uploadArea = imageField.find('.wpc-upload-area');
        const thumbnail = preview.find('.wpc-thumbnail');
        const filename = preview.find('.wpc-filename');
        const filesize = preview.find('.wpc-filesize');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Set image data
        thumbnail.attr('src', fileData.url);
        filename.text(fileData.filename);
        filesize.text(fileData.size);
        hiddenInput.val(fileData.filename);

        // Hide upload area and show preview
        uploadArea.hide();
        preview.show();

        // Mark field as valid
        imageField.removeClass('wpc-invalid').addClass('wpc-valid');

        // Show preview button if preview is enabled
        const previewBtn = imageField.find('.wpc-preview-btn');
        if (previewBtn.length > 0) {
            previewBtn.show();
            previewBtn.data('image-url', fileData.url);
        }
    }
    
    // Remove image
    function removeImage(imageField) {
        const filename = imageField.find('.wpc-uploaded-file').val();
        
        if (filename) {
            // Delete file from server
            $.ajax({
                url: wpc_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpc_delete_file',
                    filename: filename,
                    nonce: wpc_frontend.nonce
                }
            });
        }
        
        // Reset field
        const uploadArea = imageField.find('.wpc-upload-area');
        const preview = imageField.find('.wpc-image-preview');
        const fileInput = imageField.find('.wpc-file-input');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Hide preview and show upload area
        preview.hide();
        uploadArea.show();
        fileInput.val('');
        hiddenInput.val('');

        // Mark field as invalid
        imageField.removeClass('wpc-valid').addClass('wpc-invalid');
        
        clearError(imageField);
    }
    
    // Show upload progress
    function showUploadProgress(imageField, percent) {
        let progressContainer = imageField.find('.wpc-upload-progress');
        
        if (progressContainer.length === 0) {
            progressContainer = $(`
                <div class="wpc-upload-progress">
                    <div class="wpc-upload-progress-bar"></div>
                </div>
                <div class="wpc-upload-status">${wpc_frontend.strings.uploading}</div>
            `);
            imageField.find('.wpc-upload-area').after(progressContainer);
        }
        
        const progressBar = progressContainer.find('.wpc-upload-progress-bar');
        progressBar.css('width', percent + '%');
    }
    
    // Hide upload progress
    function hideUploadProgress(imageField) {
        imageField.find('.wpc-upload-progress, .wpc-upload-status').remove();
    }
    
    // Validate text field
    function validateTextField(field) {
        const value = field.val().trim();
        const isRequired = field.prop('required');
        
        if (isRequired && value === '') {
            field.removeClass('wpc-valid').addClass('wpc-invalid');
            return false;
        } else {
            field.removeClass('wpc-invalid').addClass('wpc-valid');
            return true;
        }
    }
    
    // Show error message
    function showError(field, message) {
        clearError(field);
        
        const errorDiv = $('<div class="wpc-error-message">' + message + '</div>');
        field.after(errorDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    // Clear error message
    function clearError(field) {
        field.siblings('.wpc-error-message').remove();
    }
    
    // Get field ID from image field
    function getFieldId(imageField) {
        const fileInput = imageField.find('.wpc-file-input');
        const fieldId = fileInput.attr('id').replace('wpc_field_', '');
        return fieldId;
    }
    
    // Validate form before add to cart
    function validateFormOnSubmit() {
        $('form.cart').on('submit', function(e) {
            const customizationContainer = $('#wpc-customization-fields');
            
            if (customizationContainer.length === 0) {
                return true; // No customization fields
            }
            
            let isValid = true;
            const errors = [];
            
            // Validate text fields
            customizationContainer.find('.wpc-text-field').each(function() {
                if (!validateTextField($(this))) {
                    isValid = false;
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Validate image fields
            customizationContainer.find('.wpc-image-field').each(function() {
                const hiddenInput = $(this).find('.wpc-uploaded-file');
                if (hiddenInput.val() === '') {
                    isValid = false;
                    $(this).removeClass('wpc-valid').addClass('wpc-invalid');
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Show validation messages
            const messagesContainer = customizationContainer.find('.wpc-validation-messages');
            messagesContainer.empty();
            
            if (!isValid) {
                e.preventDefault();
                
                const uniqueErrors = [...new Set(errors)];
                uniqueErrors.forEach(function(error) {
                    messagesContainer.append('<div class="wpc-error-message">' + error + '</div>');
                });
                
                // Scroll to first error
                $('html, body').animate({
                    scrollTop: customizationContainer.offset().top - 100
                }, 500);
                
                return false;
            }
            
            return true;
        });
    }

    // Add to Cart Button Control
    function initAddToCartControl() {
        // Check if customization is required
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return; // No customization fields, nothing to control
        }

        // Add CSS class to identify controlled button
        const addToCartButton = $('button[name="add-to-cart"], .single_add_to_cart_button');
        addToCartButton.addClass('wpc-controlled-button');

        // Store original button text
        if (!addToCartButton.data('original-text')) {
            addToCartButton.data('original-text', addToCartButton.text());
        }
    }

    // Validate all customization fields
    function validateAllFields() {
        // Prevent infinite recursion
        if (window.wpcValidating) {
            return true;
        }
        window.wpcValidating = true;

        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            window.wpcValidating = false;
            return true; // No customization fields
        }

        let allValid = true;
        let hasRequiredFields = false;

        // Check text fields
        customizationContainer.find('.wpc-text-field').each(function() {
            const field = $(this);
            const value = field.val().trim();

            if (field.prop('required')) {
                hasRequiredFields = true;
                if (value === '') {
                    allValid = false;
                }
            }
        });

        // Check image fields
        customizationContainer.find('.wpc-image-field').each(function() {
            const imageField = $(this);
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');

            if (fileInput.prop('required')) {
                hasRequiredFields = true;
                // Check if file is uploaded (either has files or preview is visible)
                if (!fileInput[0].files.length && !preview.is(':visible')) {
                    allValid = false;
                }
            }
        });

        // Update add to cart button state
        updateAddToCartButton(allValid, hasRequiredFields);

        // Reset recursion flag
        window.wpcValidating = false;

        return allValid;
    }

    // Update add to cart button state
    function updateAddToCartButton(isValid, hasRequiredFields) {
        const addToCartButton = $('.wpc-controlled-button');

        if (addToCartButton.length === 0) {
            return; // No button to control
        }

        const originalText = addToCartButton.data('original-text');

        // Only control button if there are required fields
        if (hasRequiredFields) {
            if (isValid) {
                // Enable button
                addToCartButton
                    .prop('disabled', false)
                    .removeClass('wpc-disabled')
                    .text(originalText);
            } else {
                // Disable button
                addToCartButton
                    .prop('disabled', true)
                    .addClass('wpc-disabled')
                    .text('Preencha os campos obrigatórios');
            }
        } else {
            // No required fields, ensure button is enabled
            addToCartButton
                .prop('disabled', false)
                .removeClass('wpc-disabled')
                .text(originalText);
        }
    }

    // Session Monitoring
    function initSessionMonitoring() {
        // Check if there are customization fields
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return;
        }

        // Check session every 2 minutes
        setInterval(checkSessionStatus, 2 * 60 * 1000);

        // Show warning at 12 minutes (3 minutes before expiry)
        setTimeout(showSessionWarning, 12 * 60 * 1000);
    }

    // Check session status
    function checkSessionStatus() {
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'wpc_check_session',
                nonce: wpc_frontend.nonce
            },
            success: function(response) {
                if (!response.success && response.data && response.data.expired) {
                    showSessionExpiredAlert(response.data.message);
                }
            },
            error: function() {
                // Silent fail - don't interrupt user experience
            }
        });
    }

    // Show session warning (3 minutes before expiry)
    function showSessionWarning() {
        // Check if user still has uploaded files
        const hasUploadedFiles = $('.wpc-image-preview:visible').length > 0;

        if (hasUploadedFiles) {
            showWordPressNotice(
                'Atenção: Sua sessão expirará em 3 minutos. Adicione o produto ao carrinho para salvar suas personalizações.',
                'warning'
            );
        }
    }

    // Show session expired alert
    function showSessionExpiredAlert(message) {
        showWordPressNotice(message, 'error');

        // Clear all uploaded files from interface
        $('.wpc-image-preview').hide();
        $('.wpc-upload-area').show();
        $('.wpc-file-input').val('');
        $('.wpc-uploaded-file').val('');

        // Reset add to cart button
        validateAllFields();
    }

    // Show WordPress-style notice
    function showWordPressNotice(message, type) {
        // Remove existing notices
        $('.wpc-session-notice').remove();

        const noticeClass = type === 'error' ? 'notice-error' : 'notice-warning';
        const notice = $(`
            <div class="notice ${noticeClass} wpc-session-notice" style="margin: 15px 0; padding: 12px; border-left: 4px solid; position: relative;">
                <p style="margin: 0;">${message}</p>
                <button type="button" class="notice-dismiss" style="position: absolute; top: 0; right: 1px; border: none; margin: 0; padding: 9px; background: none; color: #72777c; cursor: pointer;">
                    <span class="screen-reader-text">Dispensar este aviso.</span>
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);

        // Insert notice before customization fields
        $('#wpc-customization-fields').before(notice);

        // Handle dismiss button
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(300, function() {
                $(this).remove();
            });
        });

        // Auto-dismiss warning notices after 10 seconds
        if (type === 'warning') {
            setTimeout(function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 10000);
        }
    }

    // Product Preview Functions
    function generateProductPreview(imageUrl) {
        // Check if preview is enabled
        if (!wpc_frontend.preview || !wpc_frontend.preview.enabled) {
            return;
        }

        const modal = $('#wpc-preview-modal');
        const canvas = $('#wpc-preview-canvas')[0];
        const loading = $('.wpc-preview-loading');

        if (!canvas || !modal.length) {
            return;
        }

        // Show modal and loading
        modal.show();
        loading.show();

        const ctx = canvas.getContext('2d');
        const templateImg = new Image();
        const userImg = new Image();
        const maskImg = new Image();

        let imagesLoaded = 0;
        const totalImages = wpc_frontend.preview.mask ? 3 : 2;

        function checkAllImagesLoaded() {
            imagesLoaded++;
            if (imagesLoaded === totalImages) {
                drawPreview();
            }
        }

        function drawPreview() {
            const area = wpc_frontend.preview.area;

            // Set canvas size based on template
            canvas.width = templateImg.width;
            canvas.height = templateImg.height;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw template (background)
            ctx.drawImage(templateImg, 0, 0);

            // Draw user image in specified area
            if (area) {
                // Calculate aspect ratio to fit image in area
                const aspectRatio = userImg.width / userImg.height;
                const areaAspectRatio = area.width / area.height;

                let drawWidth, drawHeight, drawX, drawY;

                if (aspectRatio > areaAspectRatio) {
                    // Image is wider than area
                    drawWidth = area.width;
                    drawHeight = area.width / aspectRatio;
                    drawX = area.x;
                    drawY = area.y + (area.height - drawHeight) / 2;
                } else {
                    // Image is taller than area
                    drawHeight = area.height;
                    drawWidth = area.height * aspectRatio;
                    drawX = area.x + (area.width - drawWidth) / 2;
                    drawY = area.y;
                }

                // Save context for clipping
                ctx.save();

                // Create clipping path for the area
                ctx.beginPath();
                ctx.rect(area.x, area.y, area.width, area.height);
                ctx.clip();

                // Draw user image
                ctx.drawImage(userImg, drawX, drawY, drawWidth, drawHeight);

                // Restore context
                ctx.restore();
            } else {
                // No area specified, draw image centered
                const scale = Math.min(canvas.width / userImg.width, canvas.height / userImg.height) * 0.8;
                const scaledWidth = userImg.width * scale;
                const scaledHeight = userImg.height * scale;
                const x = (canvas.width - scaledWidth) / 2;
                const y = (canvas.height - scaledHeight) / 2;

                ctx.drawImage(userImg, x, y, scaledWidth, scaledHeight);
            }

            // Draw mask on top if available
            if (wpc_frontend.preview.mask && maskImg.complete) {
                ctx.drawImage(maskImg, 0, 0);
            }

            // Hide loading
            loading.hide();
        }

        // Load template image
        templateImg.onload = checkAllImagesLoaded;
        templateImg.onerror = function() {
            console.error('Failed to load template image');
            loading.hide();
        };
        templateImg.src = wpc_frontend.preview.template;

        // Load user image
        userImg.onload = checkAllImagesLoaded;
        userImg.onerror = function() {
            console.error('Failed to load user image');
            loading.hide();
        };
        userImg.src = imageUrl;

        // Load mask image if available
        if (wpc_frontend.preview.mask) {
            maskImg.onload = checkAllImagesLoaded;
            maskImg.onerror = function() {
                console.error('Failed to load mask image');
                checkAllImagesLoaded(); // Continue without mask
            };
            maskImg.src = wpc_frontend.preview.mask;
        }
    }

    // Preview button click handler
    $(document).on('click', '.wpc-preview-btn', function() {
        const imageUrl = $(this).data('image-url');
        if (imageUrl) {
            generateProductPreview(imageUrl);
        }
    });

    // Modal close handlers
    $(document).on('click', '.wpc-modal-close, .wpc-modal-overlay', function() {
        $('#wpc-preview-modal').hide();
    });

    // Prevent modal content click from closing modal
    $(document).on('click', '.wpc-modal-content', function(e) {
        e.stopPropagation();
    });

    // Hide preview button when image is removed
    $(document).on('click', '.wpc-remove-image', function() {
        const imageField = $(this).closest('.wpc-image-field');
        const previewBtn = imageField.find('.wpc-preview-btn');
        previewBtn.hide().removeData('image-url');
    });

    // ESC key to close modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#wpc-preview-modal').hide();
        }
    });

    // Cart Restoration Functions
    function initCartRestoration() {
        // Check if we have restoration data from URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        // Look for WPC parameters
        const wpcParams = {};
        let hasWpcData = false;

        for (const [key, value] of urlParams.entries()) {
            if (key.startsWith('wpc_')) {
                wpcParams[key] = value;
                hasWpcData = true;
            }
        }

        if (hasWpcData) {
            // Convert URL parameters to restoration data format
            const data = parseWpcParameters(wpcParams);
            restoreCustomizationData(data);

            // Clean WPC parameters from URL
            const cleanParams = new URLSearchParams(window.location.search);
            for (const key of Object.keys(wpcParams)) {
                cleanParams.delete(key);
            }

            const cleanUrl = window.location.pathname + (cleanParams.toString() ? '?' + cleanParams.toString() : '');
            window.history.replaceState({}, document.title, cleanUrl);

            // Show restoration notification
            showRestorationNotification();
        }
    }

    function parseWpcParameters(wpcParams) {
        const data = { fields: {} };

        // Group parameters by field ID
        const fieldGroups = {};

        for (const [key, value] of Object.entries(wpcParams)) {
            const match = key.match(/^wpc_(image|url|size|text)_(\d+)$/);
            if (match) {
                const [, type, fieldId] = match;

                if (!fieldGroups[fieldId]) {
                    fieldGroups[fieldId] = {};
                }

                if (type === 'image') {
                    fieldGroups[fieldId].type = 'image';
                    fieldGroups[fieldId].filename = value;
                } else if (type === 'url') {
                    fieldGroups[fieldId].url = value;
                } else if (type === 'size') {
                    fieldGroups[fieldId].size = value;
                } else if (type === 'text') {
                    fieldGroups[fieldId].type = 'text';
                    fieldGroups[fieldId].value = value;
                }
            }
        }

        // Convert to final format
        for (const [fieldId, fieldData] of Object.entries(fieldGroups)) {
            data.fields[fieldId] = fieldData;
        }

        return data;
    }

    function restoreCustomizationData(data) {
        // Wait for page to be fully loaded and check if elements exist
        function attemptRestore() {
            const customizationContainer = $('#wpc-customization-fields');
            if (customizationContainer.length === 0) {
                setTimeout(attemptRestore, 500);
                return;
            }

            // Restore customization fields (variations are handled by WooCommerce natively)
            if (data.fields) {
                Object.keys(data.fields).forEach(function(fieldId) {
                    const fieldData = data.fields[fieldId];
                    const field = $('.wpc-image-field').filter(function() {
                        return $(this).find('input[type="file"]').attr('name') === 'wpc_field_' + fieldId;
                    });

                    if (field.length > 0) {
                        if (fieldData.type === 'image' && (fieldData.url || fieldData.filename)) {
                            // If we only have filename, reconstruct the URL
                            if (!fieldData.url && fieldData.filename) {
                                const today = new Date();
                                const sessionId = 'test_session_' + today.getFullYear() + '_' +
                                                String(today.getMonth() + 1).padStart(2, '0') + '_' +
                                                String(today.getDate()).padStart(2, '0');
                                fieldData.url = window.location.origin + '/wp-content/uploads/wpc-temp/' + sessionId + '/' + fieldData.filename;
                                fieldData.size = fieldData.size || '';
                            }
                            restoreImageField(field, fieldData);
                        } else if (fieldData.type === 'text' && fieldData.value) {
                            restoreTextField(fieldId, fieldData.value);
                        }
                    }
                });
            }
        }

        // Start the restoration attempt
        attemptRestore();
    }



    function restoreImageField(imageField, fieldData) {
        const preview = imageField.find('.wpc-image-preview');
        const uploadArea = imageField.find('.wpc-upload-area');
        const thumbnail = preview.find('.wpc-thumbnail');
        const filename = preview.find('.wpc-filename');
        const filesize = preview.find('.wpc-filesize');
        const hiddenInput = imageField.find('.wpc-uploaded-file');
        const previewBtn = imageField.find('.wpc-preview-btn');

        // Set image data
        thumbnail.attr('src', fieldData.url);
        filename.text(fieldData.filename || 'Imagem restaurada');
        filesize.text(fieldData.size || '');
        hiddenInput.val(fieldData.filename || '');

        // Show preview and hide upload area
        uploadArea.hide();
        preview.show();

        // Mark field as valid
        imageField.removeClass('wpc-invalid').addClass('wpc-valid');

        // Show preview button if available
        if (previewBtn.length > 0) {
            previewBtn.show();
            previewBtn.data('image-url', fieldData.url);
        }

        // Reactivate image field functionality
        reactivateImageField(imageField);

        // Validate all fields after restoration
        validateAllFields();
    }

    function restoreTextField(fieldId, value) {
        const textField = $('input[name="wpc_field_' + fieldId + '"], textarea[name="wpc_field_' + fieldId + '"]');
        if (textField.length > 0) {
            textField.val(value);
            textField.removeClass('wpc-invalid').addClass('wpc-valid');
        }
    }

    function reactivateImageField(imageField) {
        const uploadArea = imageField.find('.wpc-upload-area');
        const fileInput = imageField.find('.wpc-file-input');
        const maxSize = parseFloat(imageField.data('max-size'));

        // Remove existing event listeners to avoid duplicates
        uploadArea.off('click dragover dragleave drop');
        fileInput.off('change');
        imageField.off('click', '.wpc-remove-image');

        // Click to upload
        uploadArea.on('click', function() {
            fileInput.click();
        });

        // File input change
        fileInput.on('change', function() {
            const file = this.files[0];
            if (file) {
                handleFileUpload(file, imageField, maxSize);
            }
        });

        // Drag and drop functionality
        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('wpc-dragover');
        });

        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('wpc-dragover');
        });

        uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('wpc-dragover');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0], imageField, maxSize);
            }
        });

        // Remove image functionality
        imageField.on('click', '.wpc-remove-image', function(e) {
            e.preventDefault();
            removeImage(imageField);

            // Check all fields after removal
            validateAllFields();
        });
    }

    function showRestorationNotification() {
        // Create notification element
        const notification = $(`
            <div class="wpc-restoration-notice">
                <div class="wpc-notice-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"></path>
                        <circle cx="12" cy="12" r="10"></circle>
                    </svg>
                    <span>✨ Personalização restaurada do carrinho!</span>
                </div>
            </div>
        `);

        // Add to page
        $('body').append(notification);

        // Show with animation
        setTimeout(function() {
            notification.addClass('wpc-notice-show');
        }, 100);

        // Hide after 4 seconds
        setTimeout(function() {
            notification.removeClass('wpc-notice-show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 4000);
    }



    // Handle the restoration parameter on product pages
    function handleCartRestoration() {
        const urlParams = new URLSearchParams(window.location.search);
        const restoreParam = urlParams.get('wpc_restore_cart_item');

        // Check if we have any restoration parameters (image or text)
        const hasRestorationParams = Array.from(urlParams.keys()).some(key =>
            key.startsWith('wpc_image_') || key.startsWith('wpc_text_')
        );

        if (restoreParam !== null || hasRestorationParams) {
            // Mark this as a restoration scenario - force image restoration
            sessionStorage.setItem('wpc_force_restore_' + getProductId(), 'true');

            // Show a message that we're trying to restore
            if ($('#wpc-customization-fields').length > 0) {
                $('#wpc-customization-fields').prepend(
                    '<div class="wpc-restore-notice" style="background: #e7f3ff; border: 1px solid #0073aa; padding: 10px; margin-bottom: 15px; border-radius: 4px;">' +
                    '🔄 <strong>Restaurando personalização do carrinho...</strong><br>' +
                    '<small>Se a imagem não aparecer automaticamente, faça upload novamente.</small>' +
                    '</div>'
                );

                // Remove the notice after 5 seconds
                setTimeout(function() {
                    $('.wpc-restore-notice').fadeOut();
                }, 5000);
            }

            // Clean URL
            const cleanUrl = window.location.pathname + window.location.search.replace(/[?&]wpc_restore_cart_item=[^&]*/, '').replace(/^&/, '?');
            window.history.replaceState({}, document.title, cleanUrl);
        }
    }

    // Image Persistence System
    function initImagePersistence() {

        // Save image data when uploaded
        $(document).on('wpc_image_uploaded', function(e, fieldId, imageData) {
            saveImageToSession(fieldId, imageData);
        });

        // Restore images on page load
        restoreImagesFromSession();

        // Clear session data when form is successfully submitted
        $(document).on('wpc_form_submitted', function() {
            // Don't clear session - keep images for additional variations
        });

        // Don't clear session when product is added to cart - keep for additional variations

        // Clear session data when cart is updated (item removed)
        $(document.body).on('updated_cart_totals', function() {
            // The server-side script will handle clearing specific product sessions
        });
    }

    function saveImageToSession(fieldId, imageData) {
        try {
            const sessionKey = 'wpc_images_' + getProductId();
            let sessionData = JSON.parse(sessionStorage.getItem(sessionKey) || '{}');

            sessionData[fieldId] = {
                filename: imageData.filename,
                url: imageData.url,
                size: imageData.size,
                timestamp: Date.now()
            };

            sessionStorage.setItem(sessionKey, JSON.stringify(sessionData));
            console.log('WPC DEBUG: Saved image to session:', fieldId, sessionData[fieldId]);
        } catch (e) {
            console.error('WPC: Error saving image to session:', e);
        }
    }

    function restoreImagesFromSession() {
        try {
            console.log('WPC DEBUG: restoreImagesFromSession called');

            // Simple logic: if we have images in session OR restoration params, restore them
            if (!shouldRestoreImages()) {
                console.log('WPC DEBUG: Should not restore images');
                return;
            }

            const sessionKey = 'wpc_images_' + getProductId();
            const sessionData = JSON.parse(sessionStorage.getItem(sessionKey) || '{}');

            console.log('WPC DEBUG: Session data found:', sessionData);

            if (Object.keys(sessionData).length > 0) {
                Object.keys(sessionData).forEach(function(fieldId) {
                    const imageData = sessionData[fieldId];
                    console.log('WPC DEBUG: Processing field', fieldId, 'with data:', imageData);

                    // Check if image is not too old (15 minutes)
                    const maxAge = 15 * 60 * 1000; // 15 minutes
                    const age = Date.now() - imageData.timestamp;
                    console.log('WPC DEBUG: Image age:', age, 'ms, max age:', maxAge, 'ms');

                    if (age < maxAge) {
                        const field = $('.wpc-image-field').filter(function() {
                            return $(this).find('input[type="file"]').attr('name') === 'wpc_field_' + fieldId;
                        });

                        console.log('WPC DEBUG: Found field for restoration:', field.length > 0);

                        if (field.length > 0) {
                            console.log('WPC DEBUG: Restoring image for field', fieldId);
                            restoreImageField(field, imageData);
                        }
                    } else {
                        console.log('WPC DEBUG: Image too old, removing from session');
                        delete sessionData[fieldId];
                    }
                });

                // Update session data (remove old images)
                sessionStorage.setItem(sessionKey, JSON.stringify(sessionData));
            } else {
                console.log('WPC DEBUG: No session data to restore');
            }
        } catch (e) {
            console.error('WPC: Error restoring images from session:', e);
        }
    }

    function shouldRestoreImages() {
        console.log('WPC DEBUG: shouldRestoreImages() called');

        // Method 1: Check if we came from cart (restoration scenario)
        const urlParams = new URLSearchParams(window.location.search);
        const hasRestorationParams = Array.from(urlParams.keys()).some(key =>
            key.startsWith('wpc_image_') || key.startsWith('wpc_text_')
        );

        if (hasRestorationParams) {
            console.log('WPC DEBUG: Has restoration params in URL - should restore');
            return true;
        }

        // Method 2: Check if this is a forced restoration from cart
        const forceRestoreKey = 'wpc_force_restore_' + getProductId();
        if (sessionStorage.getItem(forceRestoreKey)) {
            sessionStorage.removeItem(forceRestoreKey);
            console.log('WPC DEBUG: Forced restoration from cart');
            return true;
        }

        // Method 3: Check if we have images in session (any page load with existing images should restore)
        const sessionKey = 'wpc_images_' + getProductId();
        const sessionData = JSON.parse(sessionStorage.getItem(sessionKey) || '{}');

        if (Object.keys(sessionData).length > 0) {
            console.log('WPC DEBUG: Found images in session - should restore');
            return true;
        }

        console.log('WPC DEBUG: No images to restore');
        return false;
    }

    function clearImageSession() {
        try {
            const productId = getProductId();
            const sessionKey = 'wpc_images_' + productId;

            // Clear image data
            sessionStorage.removeItem(sessionKey);
            console.log('WPC DEBUG: Cleared image session for product', productId);
        } catch (e) {
            console.error('WPC: Error clearing image session:', e);
        }
    }

    function getProductId() {
        // Try to get product ID from various sources
        const productIdInput = $('input[name="product_id"]');
        if (productIdInput.length > 0) {
            return productIdInput.val();
        }

        // Fallback: extract from URL
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 2] || 'unknown';
    }

    // ========================================
    // TESTE: Sistema de Upload Local (localStorage)
    // ========================================

    function initLocalImageFields() {
        $('.wpc-image-field-test').each(function() {
            const imageField = $(this);
            const uploadArea = imageField.find('.wpc-upload-area-test');
            const fileInput = imageField.find('.wpc-file-input-test');
            const preview = imageField.find('.wpc-image-preview-test');
            const maxSize = parseFloat(imageField.data('max-size'));
            const fieldId = imageField.data('field-id');

            // Click to upload
            uploadArea.on('click', function() {
                fileInput.click();
            });

            // File input change
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    handleLocalFileUpload(file, imageField, maxSize, fieldId);
                }
            });

            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('wpc-dragover');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleLocalFileUpload(files[0], imageField, maxSize, fieldId);
                }
            });

            // Remove image button
            imageField.on('click', '.wpc-remove-image-test', function() {
                removeLocalImage(imageField, fieldId);
            });

            // Preview button
            imageField.on('click', '.wpc-preview-btn-test', function() {
                const imageData = getLocalImageData(fieldId);
                if (imageData) {
                    generateProductPreview(imageData.dataUrl);
                }
            });

            // Load existing image from localStorage on page load
            loadLocalImageFromStorage(imageField, fieldId);
        });
    }

    function handleLocalFileUpload(file, imageField, maxSize, fieldId) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            showLocalError(imageField, 'Formato inválido. Use apenas PNG ou JPG.');
            return;
        }

        // Validate file size
        const maxSizeBytes = maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            showLocalError(imageField, `Arquivo muito grande. Máximo: ${maxSize} MB`);
            return;
        }

        // Read file as base64
        const reader = new FileReader();
        reader.onload = function(e) {
            const dataUrl = e.target.result;

            // Validate image dimensions
            const img = new Image();
            img.onload = function() {
                if (img.width < 300 || img.height < 300) {
                    showLocalError(imageField, 'Resolução muito baixa. Use imagens com pelo menos 300x300 pixels.');
                    return;
                }

                // Store in localStorage
                const imageData = {
                    dataUrl: dataUrl,
                    filename: file.name,
                    size: file.size,
                    type: file.type,
                    timestamp: Date.now()
                };

                storeLocalImageData(fieldId, imageData);
                displayLocalImage(imageField, imageData);
                hideLocalError(imageField);

                // Trigger validation
                validateAllFields();
            };

            img.onerror = function() {
                showLocalError(imageField, 'Arquivo de imagem inválido.');
            };

            img.src = dataUrl;
        };

        reader.onerror = function() {
            showLocalError(imageField, 'Erro ao ler o arquivo.');
        };

        reader.readAsDataURL(file);
    }

    function storeLocalImageData(fieldId, imageData) {
        const key = `wpc_test_image_${fieldId}`;
        try {
            localStorage.setItem(key, JSON.stringify(imageData));
        } catch (e) {
            console.error('Erro ao salvar imagem no localStorage:', e);
        }
    }

    // Tornar função global para acesso no carrinho
    window.getLocalImageData = function(fieldId) {
        const key = `wpc_test_image_${fieldId}`;
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (e) {
            console.error('Erro ao recuperar imagem do localStorage:', e);
            return null;
        }
    };

    function removeLocalImageData(fieldId) {
        const key = `wpc_test_image_${fieldId}`;
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('Erro ao remover imagem do localStorage:', e);
        }
    }

    function displayLocalImage(imageField, imageData) {
        const preview = imageField.find('.wpc-image-preview-test');
        const thumbnail = imageField.find('.wpc-thumbnail-test');
        const filename = imageField.find('.wpc-filename-test');
        const filesize = imageField.find('.wpc-filesize-test');
        const previewBtn = imageField.find('.wpc-preview-btn-test');
        const hiddenInput = imageField.find('.wpc-uploaded-file-test');
        const uploadArea = imageField.find('.wpc-upload-area-test');

        // Update preview
        thumbnail.attr('src', imageData.dataUrl);
        filename.text(imageData.filename);
        filesize.text(formatFileSize(imageData.size));
        hiddenInput.val('local_' + Date.now()); // Dummy value to indicate image is present

        // Show preview, hide upload area
        preview.show();
        previewBtn.show();
        uploadArea.hide();
    }

    function removeLocalImage(imageField, fieldId) {
        const preview = imageField.find('.wpc-image-preview-test');
        const previewBtn = imageField.find('.wpc-preview-btn-test');
        const hiddenInput = imageField.find('.wpc-uploaded-file-test');
        const uploadArea = imageField.find('.wpc-upload-area-test');

        // Remove from localStorage
        removeLocalImageData(fieldId);

        // Reset UI
        preview.hide();
        previewBtn.hide();
        hiddenInput.val('');
        uploadArea.show();

        // Clear file input
        imageField.find('.wpc-file-input-test').val('');

        // Trigger validation
        validateAllFields();
    }

    function loadLocalImageFromStorage(imageField, fieldId) {
        const imageData = getLocalImageData(fieldId);
        if (imageData) {
            displayLocalImage(imageField, imageData);
        }
    }

    function showLocalError(imageField, message) {
        // Remove existing error
        imageField.find('.wpc-local-error').remove();

        // Add error message
        const errorDiv = $('<div class="wpc-local-error" style="color: #ff6b35; font-size: 12px; margin-top: 5px;">' + message + '</div>');
        imageField.append(errorDiv);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut();
        }, 5000);
    }

    function hideLocalError(imageField) {
        imageField.find('.wpc-local-error').remove();
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Initialize local image fields
    initLocalImageFields();

    // TESTE: Carregar imagens locais no carrinho (função global para acesso externo)
    window.loadLocalImagesInCart = function() {
        console.log('🧪 TESTE: Tentando carregar imagens locais no carrinho...');

        // Verificar se estamos na página do carrinho ou se há containers de imagem
        let containers = $('[id^="wpc-local-image-"]');

        // Tentar também pela classe como alternativa
        if (containers.length === 0) {
            containers = $('.wpc-local-image-container');
        }

        console.log('🧪 TESTE: Containers encontrados:', containers.length);
        console.log('🧪 TESTE: Página atual:', window.location.pathname);
        console.log('🧪 TESTE: Body classes:', $('body').attr('class'));

        if (containers.length === 0) {
            console.log('🧪 TESTE: Nenhum container de imagem local encontrado');
            // Listar todos os elementos que começam com wpc para debug
            const allWpcElements = $('[id*="wpc"]');
            console.log('🧪 TESTE: Elementos WPC encontrados:', allWpcElements.length);
            allWpcElements.each(function() {
                console.log('🧪 TESTE: Elemento WPC:', $(this).attr('id'), $(this).attr('class'));
            });
            return;
        }

        // Procurar por containers de imagem local no carrinho
        containers.each(function() {
            const container = $(this);
            let fieldId = '';

            // Tentar obter field ID do atributo id
            if (container.attr('id') && container.attr('id').includes('wpc-local-image-')) {
                fieldId = container.attr('id').replace('wpc-local-image-', '');
            }
            // Tentar obter field ID do data-attribute
            else if (container.data('field-id')) {
                fieldId = container.data('field-id');
            }

            console.log('🧪 TESTE: Processando field ID:', fieldId);
            console.log('🧪 TESTE: Container ID:', container.attr('id'));
            console.log('🧪 TESTE: Container data-field-id:', container.data('field-id'));

            if (!fieldId) {
                console.log('🧪 TESTE: Field ID não encontrado para container');
                return;
            }

            const imageData = window.getLocalImageData(fieldId);
            console.log('🧪 TESTE: Dados da imagem:', imageData ? 'encontrados' : 'não encontrados');

            if (imageData) {
                // Substituir o conteúdo do container pela imagem
                container.html('<img src="' + imageData.dataUrl + '" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;" alt="Imagem local">');
                console.log('🧪 TESTE: Imagem carregada para field ID:', fieldId);
            } else {
                // Mostrar que não há imagem
                container.html('<div style="font-size: 9px; text-align: center;">Sem imagem local</div>');
                console.log('🧪 TESTE: Nenhuma imagem local para field ID:', fieldId);

                // Debug: Listar todas as chaves do localStorage
                console.log('🧪 TESTE: Chaves no localStorage:');
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.includes('wpc_test_image')) {
                        console.log('🧪 TESTE: Chave encontrada:', key);
                    }
                }
            }
        });
    };

    // Carregar imagens locais no carrinho quando a página carregar
    // Tentar múltiplas vezes para garantir que o DOM está pronto
    setTimeout(function() {
        console.log('🧪 TESTE: Primeira tentativa (500ms)');
        loadLocalImagesInCart();
    }, 500);

    setTimeout(function() {
        console.log('🧪 TESTE: Segunda tentativa (1s)');
        loadLocalImagesInCart();
    }, 1000);

    setTimeout(function() {
        console.log('🧪 TESTE: Terceira tentativa (2s)');
        loadLocalImagesInCart();
    }, 2000);

    // Recarregar imagens quando o carrinho for atualizado via AJAX
    $(document.body).on('updated_wc_div', function() {
        console.log('🧪 TESTE: Carrinho atualizado via AJAX');
        setTimeout(loadLocalImagesInCart, 500);
    });

    // Também tentar quando a página terminar de carregar completamente
    $(window).on('load', function() {
        console.log('🧪 TESTE: Window load completo');
        setTimeout(loadLocalImagesInCart, 1000);
    });

    // Tentar também no document ready (caso ainda não tenha executado)
    $(document).ready(function() {
        console.log('🧪 TESTE: Document ready');
        setTimeout(loadLocalImagesInCart, 100);
    });

    // TESTE: Detectar restauração de imagem local via URL
    function handleLocalImageRestoration() {
        const urlParams = new URLSearchParams(window.location.search);
        const restoreLocal = urlParams.get('wpc_restore_local');
        const fieldId = urlParams.get('wpc_field_id');

        if (restoreLocal === '1' && fieldId) {
            // Aguardar um pouco para garantir que os campos foram inicializados
            setTimeout(function() {
                const imageData = getLocalImageData(fieldId);
                if (imageData) {
                    // Encontrar o campo correspondente e garantir que a imagem seja exibida
                    const imageField = $('.wpc-image-field-test[data-field-id="' + fieldId + '"]');
                    if (imageField.length) {
                        displayLocalImage(imageField, imageData);

                        // Scroll suave até o campo
                        $('html, body').animate({
                            scrollTop: imageField.offset().top - 100
                        }, 1000);

                        // Destacar o campo temporariamente
                        imageField.css('box-shadow', '0 0 20px #ff6b35');
                        setTimeout(function() {
                            imageField.css('box-shadow', '');
                        }, 3000);
                    }
                }

                // Limpar os parâmetros da URL sem recarregar a página
                if (window.history && window.history.replaceState) {
                    const newUrl = window.location.pathname + window.location.search.replace(/[?&]wpc_restore_local=1/, '').replace(/[?&]wpc_field_id=\d+/, '');
                    window.history.replaceState({}, document.title, newUrl);
                }
            }, 1000);
        }
    }

    // Executar restauração se necessário
    handleLocalImageRestoration();

    // TESTE: Detectar restauração de imagem do cache do servidor
    function handleCacheImageRestoration() {
        const urlParams = new URLSearchParams(window.location.search);
        const restoreCache = urlParams.get('wpc_restore_cache');
        const fieldId = urlParams.get('wpc_field_id');

        if (restoreCache === '1' && fieldId) {
            console.log('🚀 CACHE TESTE: Restaurando imagem do cache para field ID:', fieldId);

            // Aguardar um pouco para garantir que os campos foram inicializados
            setTimeout(function() {
                const imageField = $('.wpc-image-field-cache[data-field-id="' + fieldId + '"]');
                if (imageField.length) {
                    getCachedImageData(fieldId, function(imageData) {
                        if (imageData) {
                            displayCacheImage(imageField, imageData);

                            // Scroll suave até o campo
                            $('html, body').animate({
                                scrollTop: imageField.offset().top - 100
                            }, 1000);

                            // Destacar o campo temporariamente
                            imageField.css('box-shadow', '0 0 20px #2196F3');
                            setTimeout(function() {
                                imageField.css('box-shadow', '');
                            }, 3000);

                            console.log('🚀 CACHE TESTE: Imagem restaurada com sucesso');
                        } else {
                            console.log('🚀 CACHE TESTE: Nenhuma imagem encontrada no cache');
                        }
                    });
                } else {
                    console.log('🚀 CACHE TESTE: Campo não encontrado para field ID:', fieldId);
                }

                // Limpar os parâmetros da URL sem recarregar a página
                if (window.history && window.history.replaceState) {
                    const newUrl = window.location.pathname + window.location.search
                        .replace(/[?&]wpc_restore_cache=1/, '')
                        .replace(/[?&]wpc_field_id=\d+/, '');
                    window.history.replaceState({}, document.title, newUrl);
                }
            }, 1000);
        }
    }

    // Executar restauração do cache se necessário
    handleCacheImageRestoration();

    // ========================================
    // TESTE: Sistema de Upload com Cache do Servidor
    // ========================================

    function initCacheImageFields() {
        $('.wpc-image-field-cache').each(function() {
            const imageField = $(this);
            const uploadArea = imageField.find('.wpc-upload-area-cache');
            const fileInput = imageField.find('.wpc-file-input-cache');
            const preview = imageField.find('.wpc-image-preview-cache');
            const maxSize = parseFloat(imageField.data('max-size'));
            const fieldId = imageField.data('field-id');

            // Click to upload
            uploadArea.on('click', function() {
                fileInput.click();
            });

            // File input change
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    handleCacheFileUpload(file, imageField, maxSize, fieldId);
                }
            });

            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('wpc-dragover');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleCacheFileUpload(files[0], imageField, maxSize, fieldId);
                }
            });

            // Remove image button
            imageField.on('click', '.wpc-remove-image-cache', function() {
                removeCacheImage(imageField, fieldId);
            });

            // Preview button
            imageField.on('click', '.wpc-preview-btn-cache', function() {
                getCachedImageData(fieldId, function(imageData) {
                    if (imageData) {
                        generateProductPreview(imageData.data_url);
                    }
                });
            });

            // Load existing image from cache on page load
            loadCacheImageFromServer(imageField, fieldId);

            // CORREÇÃO: Também tentar carregar após um delay para garantir que a sessão está pronta
            setTimeout(function() {
                loadCacheImageFromServer(imageField, fieldId);
            }, 1000);
        });
    }

    function handleCacheFileUpload(file, imageField, maxSize, fieldId) {
        // Show loading state
        showCacheLoading(imageField, 'Enviando para servidor...');

        // Prepare form data
        const formData = new FormData();
        formData.append('action', 'wpc_upload_file_cache');
        formData.append('nonce', wpc_frontend.nonce);
        formData.append('file', file);
        formData.append('field_id', fieldId);
        formData.append('max_size', maxSize);

        // Upload via AJAX
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                hideCacheLoading(imageField);

                if (response.success) {
                    displayCacheImage(imageField, response.data);
                    hideCacheError(imageField);

                    // Trigger validation
                    validateAllFields();

                    console.log('🚀 CACHE TESTE: Upload realizado com sucesso usando:', response.data.cache_used);
                } else {
                    showCacheError(imageField, response.data.message);
                }
            },
            error: function() {
                hideCacheLoading(imageField);
                showCacheError(imageField, 'Erro de conexão. Tente novamente.');
            }
        });
    }

    function removeCacheImage(imageField, fieldId) {
        // Show loading
        showCacheLoading(imageField, 'Removendo...');

        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'wpc_delete_file_cache',
                nonce: wpc_frontend.nonce,
                field_id: fieldId
            },
            success: function(response) {
                hideCacheLoading(imageField);

                if (response.success) {
                    resetCacheImageField(imageField);
                    validateAllFields();
                    console.log('🚀 CACHE TESTE: Imagem removida do cache');
                } else {
                    showCacheError(imageField, 'Erro ao remover imagem.');
                }
            },
            error: function() {
                hideCacheLoading(imageField);
                showCacheError(imageField, 'Erro de conexão.');
            }
        });
    }

    function getCachedImageData(fieldId, callback) {
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'wpc_get_cached_image',
                nonce: wpc_frontend.nonce,
                field_id: fieldId
            },
            success: function(response) {
                if (response.success) {
                    callback(response.data);
                } else {
                    callback(null);
                }
            },
            error: function() {
                callback(null);
            }
        });
    }

    function loadCacheImageFromServer(imageField, fieldId) {
        const urlParams = new URLSearchParams(window.location.search);
        const restoreCache = urlParams.get('wpc_restore_cache');
        const urlFieldId = urlParams.get('wpc_field_id');

        // CORREÇÃO: Sempre tentar carregar se há imagem no cache
        // Só bloquear se for acesso direto E não há parâmetro de restauração
        const isDirectAccess = restoreCache !== '1' && !document.referrer.includes('/cart');

        if (isDirectAccess) {
            console.log('🚀 CACHE TESTE: Não carregando imagem para field', fieldId, '(acesso direto)');
            return;
        }

        console.log('🚀 CACHE TESTE: Tentando carregar imagem do cache para field', fieldId);
        getCachedImageData(fieldId, function(imageData) {
            if (imageData) {
                displayCacheImage(imageField, imageData);
                console.log('🚀 CACHE TESTE: Imagem restaurada do cache para field', fieldId);

                // Se veio do carrinho, destacar o campo
                if (restoreCache === '1' && urlFieldId == fieldId) {
                    // Scroll suave até o campo
                    $('html, body').animate({
                        scrollTop: imageField.offset().top - 100
                    }, 1000);

                    // Destacar o campo temporariamente
                    imageField.css('box-shadow', '0 0 20px #2196F3');
                    setTimeout(function() {
                        imageField.css('box-shadow', '');
                    }, 3000);
                }
            } else {
                console.log('🚀 CACHE TESTE: Nenhuma imagem encontrada no cache para field', fieldId);
            }
        });
    }

    // Funções auxiliares para cache
    function displayCacheImage(imageField, imageData) {
        const preview = imageField.find('.wpc-image-preview-cache');
        const thumbnail = imageField.find('.wpc-thumbnail-cache');
        const filename = imageField.find('.wpc-filename-cache');
        const filesize = imageField.find('.wpc-filesize-cache');
        const cacheInfo = imageField.find('.wpc-cache-info');
        const previewBtn = imageField.find('.wpc-preview-btn-cache');
        const hiddenInput = imageField.find('.wpc-uploaded-file-cache');
        const uploadArea = imageField.find('.wpc-upload-area-cache');

        // Update preview
        thumbnail.attr('src', imageData.data_url);
        filename.text(imageData.filename);
        filesize.text(imageData.size);
        cacheInfo.text('Cache: ' + (imageData.cache_used || 'Servidor'));
        hiddenInput.val('cache_' + Date.now()); // Dummy value to indicate image is present

        // Show preview, hide upload area
        preview.show();
        previewBtn.show();
        uploadArea.hide();
    }

    function resetCacheImageField(imageField) {
        const preview = imageField.find('.wpc-image-preview-cache');
        const previewBtn = imageField.find('.wpc-preview-btn-cache');
        const hiddenInput = imageField.find('.wpc-uploaded-file-cache');
        const uploadArea = imageField.find('.wpc-upload-area-cache');

        // Reset UI
        preview.hide();
        previewBtn.hide();
        hiddenInput.val('');
        uploadArea.show();

        // Clear file input
        imageField.find('.wpc-file-input-cache').val('');
    }

    function showCacheLoading(imageField, message) {
        // Remove existing loading
        imageField.find('.wpc-cache-loading').remove();

        // Add loading message
        const loadingDiv = $('<div class="wpc-cache-loading" style="color: #2196F3; font-size: 12px; margin-top: 5px; text-align: center;">' + message + '</div>');
        imageField.append(loadingDiv);
    }

    function hideCacheLoading(imageField) {
        imageField.find('.wpc-cache-loading').remove();
    }

    function showCacheError(imageField, message) {
        // Remove existing error
        imageField.find('.wpc-cache-error').remove();

        // Add error message
        const errorDiv = $('<div class="wpc-cache-error" style="color: #f44336; font-size: 12px; margin-top: 5px;">' + message + '</div>');
        imageField.append(errorDiv);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut();
        }, 5000);
    }

    function hideCacheError(imageField) {
        imageField.find('.wpc-cache-error').remove();
    }

    // Initialize cache image fields
    initCacheImageFields();

    // CORREÇÃO: Função global para debug e forçar carregamento
    window.forceLoadCacheImages = function() {
        console.log('🚀 CACHE TESTE: Forçando carregamento de todas as imagens do cache...');
        $('.wpc-image-field-cache').each(function() {
            const imageField = $(this);
            const fieldId = imageField.data('field-id');
            console.log('🚀 CACHE TESTE: Tentando carregar field ID:', fieldId);

            getCachedImageData(fieldId, function(imageData) {
                if (imageData) {
                    displayCacheImage(imageField, imageData);
                    console.log('🚀 CACHE TESTE: Imagem carregada para field ID:', fieldId);
                } else {
                    console.log('🚀 CACHE TESTE: Nenhuma imagem para field ID:', fieldId);
                }
            });
        });
    };

    // CORREÇÃO: Tentar carregar imagens após página totalmente carregada
    $(window).on('load', function() {
        setTimeout(function() {
            console.log('🚀 CACHE TESTE: Tentativa final de carregamento após window.load');
            window.forceLoadCacheImages();
        }, 2000);
    });

});
