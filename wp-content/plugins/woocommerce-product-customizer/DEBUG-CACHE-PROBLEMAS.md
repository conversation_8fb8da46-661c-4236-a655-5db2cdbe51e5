# 🐛 Debug - Problemas do Cache

## 🔍 Problemas Identificados

1. **Imagem não aparece no campo após reload da página**
2. **Imagem não aparece no carrinho**

## 🛠️ Correções Aplicadas

### **Correção 1: Lógica de Carregamento Melhorada**
- ✅ Modificada função `loadCacheImageFromServer()` para ser menos restritiva
- ✅ Agora verifica `document.referrer` para detectar se veio do carrinho
- ✅ Múltiplas tentativas de carregamento (imediato, 1s, 2s após window.load)

### **Correção 2: Logs de Debug Adicionados**
- ✅ Logs no upload para verificar se está salvando
- ✅ Logs no carrinho para verificar se está recuperando
- ✅ Logs no carregamento da página

### **Correção 3: Função de Debug Global**
- ✅ `window.forceLoadCacheImages()` para forçar carregamento manual

## 🧪 Como Debugar

### **Passo 1: Ativar Logs do WordPress**
No `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### **Passo 2: Testar Upload**
1. Faça upload de uma imagem no campo azul
2. Verifique no console do navegador se aparece:
   ```
   🚀 CACHE TESTE: Upload realizado com sucesso usando: APCu
   ```
3. Verifique nos logs do PHP (`/wp-content/debug.log`):
   ```
   🚀 CACHE TESTE: Upload realizado - Session: xxx, Field: xxx, Arquivo: xxx
   ```

### **Passo 3: Testar Carregamento na Página**
1. Após upload, recarregue a página
2. Abra console do navegador (F12)
3. Execute manualmente:
   ```javascript
   forceLoadCacheImages();
   ```
4. Deve aparecer:
   ```
   🚀 CACHE TESTE: Imagem carregada para field ID: X
   ```

### **Passo 4: Testar Carrinho**
1. Adicione produto ao carrinho
2. Vá para o carrinho
3. Verifique nos logs do PHP se aparece:
   ```
   🚀 CACHE TESTE: Verificando cache no carrinho - Session: xxx, Field: xxx, Imagem: encontrada
   🚀 CACHE TESTE: Imagem exibida no carrinho - Field: xxx, Tamanho: xxx chars
   ```

### **Passo 5: Verificar Cache Manualmente**
No console do navegador:
```javascript
// Verificar se há dados no cache
fetch(wpc_frontend.ajax_url, {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'action=wpc_get_cached_image&nonce=' + wpc_frontend.nonce + '&field_id=1'
}).then(r => r.json()).then(data => {
    console.log('Cache data:', data);
    if (data.success) {
        console.log('✅ Imagem encontrada no cache');
    } else {
        console.log('❌ Imagem NÃO encontrada no cache');
    }
});
```

## 🔧 Possíveis Causas

### **Causa 1: Sessão não está sendo mantida**
- **Sintoma**: Upload funciona, mas não carrega depois
- **Verificação**: Comparar session_id nos logs de upload vs carregamento
- **Solução**: Verificar cookies e configuração de sessão

### **Causa 2: APCu não está funcionando**
- **Sintoma**: Logs mostram "Transient usado como fallback"
- **Verificação**: Verificar se APCu está instalado e ativo
- **Comando**: `php -m | grep apcu`

### **Causa 3: Cache está expirando muito rápido**
- **Sintoma**: Upload funciona, mas cache some rapidamente
- **Verificação**: Verificar TTL nas configurações
- **Solução**: Aumentar `SESSION_TTL` na classe `WPC_Cache_Manager`

### **Causa 4: Problema de timing**
- **Sintoma**: Às vezes funciona, às vezes não
- **Verificação**: Logs mostram tentativas de carregamento
- **Solução**: Aumentar delays nas tentativas

## 🎯 Testes Específicos

### **Teste 1: Verificar APCu**
```php
// Adicionar temporariamente em algum lugar do código
if (function_exists('apcu_enabled') && apcu_enabled()) {
    error_log('✅ APCu está disponível e ativo');
    $info = apcu_cache_info();
    error_log('APCu info: ' . print_r($info, true));
} else {
    error_log('❌ APCu NÃO está disponível');
}
```

### **Teste 2: Verificar Sessão**
```php
// No método get_or_create_session_id()
error_log('Session ID atual: ' . $session_id);
error_log('User ID: ' . get_current_user_id());
error_log('IP: ' . $_SERVER['REMOTE_ADDR']);
```

### **Teste 3: Verificar Cache Diretamente**
```php
// Testar cache diretamente
$test_data = array('test' => 'data', 'timestamp' => time());
$result = WPC_Cache_Manager::store('test_key', $test_data, 300);
error_log('Store result: ' . ($result ? 'SUCCESS' : 'FAILED'));

$retrieved = WPC_Cache_Manager::get('test_key');
error_log('Retrieved data: ' . print_r($retrieved, true));
```

## 🚨 Ações Imediatas

### **Se Imagem Não Carrega na Página:**
1. Execute `forceLoadCacheImages()` no console
2. Verifique logs do PHP para session_id
3. Teste cache manualmente com JavaScript acima

### **Se Imagem Não Aparece no Carrinho:**
1. Verifique logs do PHP no carrinho
2. Confirme se session_id é o mesmo
3. Teste se cache tem dados com JavaScript

### **Se Nada Funciona:**
1. Verifique se APCu está instalado: `php -m | grep apcu`
2. Teste com Transients apenas (desabilitar APCu temporariamente)
3. Aumentar TTL para 24 horas para teste

## 📝 Próximos Passos

1. **Execute os testes acima** e reporte os resultados
2. **Verifique os logs** do PHP e JavaScript
3. **Teste a função manual** `forceLoadCacheImages()`
4. **Informe qual teste falha** para ajuste específico

Com essas informações, posso identificar exatamente onde está o problema! 🔍
