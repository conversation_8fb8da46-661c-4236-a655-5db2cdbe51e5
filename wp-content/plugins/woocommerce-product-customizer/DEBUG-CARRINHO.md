# 🐛 Debug - Problema no Carrinho

## 🔍 Problema Identificado
A imagem local não está sendo carregada na página do carrinho.

## 🧪 Passos para Debug

### 1. **Verificar Console do Navegador**
1. Abra o carrinho (F12 → Console)
2. Procure por logs que começam com "🧪 TESTE:"
3. Verifique se aparecem:
   - "Tentando carregar imagens locais no carrinho..."
   - "Containers encontrados: X"
   - "Field ID não encontrado" ou "Dados da imagem: encontrados/não encontrados"

### 2. **Verificar HTML Gerado**
1. No carrinho, clique com botão direito → "Inspecionar elemento"
2. Procure por elementos com:
   - `id="wpc-local-image-X"` (onde X é o field ID)
   - `class="wpc-local-image-container"`
   - Texto "🧪 TESTE - Cache Local"

### 3. **Verificar localStorage**
1. No console do navegador, digite:
```javascript
// Listar todas as chaves
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.includes('wpc_test_image')) {
        console.log('Chave:', key, 'Valor:', localStorage.getItem(key));
    }
}

// Ou verificar uma chave específica (substitua X pelo field ID)
localStorage.getItem('wpc_test_image_X');
```

### 4. **Forçar Carregamento Manual**
No console do navegador:
```javascript
// Forçar carregamento das imagens
loadLocalImagesInCart();

// Ou verificar se a função existe
typeof loadLocalImagesInCart;
```

## 🔧 Possíveis Causas

### **Causa 1: HTML não está sendo gerado**
- Verificar se o produto tem personalização de imagem
- Verificar se o item está realmente no carrinho
- Verificar logs do PHP (se WP_DEBUG ativo)

### **Causa 2: JavaScript não está carregando**
- Verificar se o arquivo frontend.js está sendo carregado
- Verificar se não há erros JavaScript bloqueando a execução

### **Causa 3: Timing de execução**
- O JavaScript pode estar executando antes do HTML estar pronto
- Tentativas múltiplas foram adicionadas (500ms, 1s, 2s)

### **Causa 4: Seletor CSS não encontra elementos**
- Verificar se os IDs estão sendo gerados corretamente
- Verificar se não há conflitos com outros plugins

## 🛠️ Soluções Implementadas

### **Logs de Debug Adicionados:**
- ✅ Console logs detalhados
- ✅ Listagem de elementos WPC encontrados
- ✅ Verificação de chaves do localStorage
- ✅ Debug do field ID no HTML

### **Múltiplas Tentativas:**
- ✅ Execução em 500ms, 1s e 2s
- ✅ Execução no window.load
- ✅ Execução no updated_wc_div (AJAX)

### **Seletores Alternativos:**
- ✅ Busca por ID: `[id^="wpc-local-image-"]`
- ✅ Busca por classe: `.wpc-local-image-container`
- ✅ Busca por data-attribute: `data-field-id`

### **Funções Globais:**
- ✅ `window.loadLocalImagesInCart`
- ✅ `window.getLocalImageData`

## 📝 Próximos Passos

1. **Testar com logs ativos** e verificar o que aparece no console
2. **Verificar se o HTML está sendo gerado** inspecionando o elemento
3. **Confirmar se há dados no localStorage** com os comandos acima
4. **Reportar os resultados** para ajuste fino da solução

## 🎯 Teste Rápido

Execute este código no console do carrinho:
```javascript
console.log('=== DEBUG CARRINHO ===');
console.log('Containers por ID:', $('[id^="wpc-local-image-"]').length);
console.log('Containers por classe:', $('.wpc-local-image-container').length);
console.log('localStorage keys:', Object.keys(localStorage).filter(k => k.includes('wpc_test')));
console.log('Função loadLocalImagesInCart:', typeof window.loadLocalImagesInCart);
console.log('Função getLocalImageData:', typeof window.getLocalImageData);
```

Este debug deve nos dar todas as informações necessárias para identificar e corrigir o problema! 🔍
