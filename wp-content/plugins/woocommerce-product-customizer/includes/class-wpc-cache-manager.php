<?php
/**
 * Cache Manager class for WooCommerce Product Customizer
 * TESTE: Sistema de cache no servidor usando APCu com fallback para Transients
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPC_Cache_Manager {

    /**
     * Cache key prefix
     */
    const CACHE_PREFIX = 'wpc_cache_test_';

    /**
     * Default TTL (15 minutes)
     */
    const DEFAULT_TTL = 900;

    /**
     * Session TTL (1 hour for cart workflow)
     */
    const SESSION_TTL = 3600;

    /**
     * Store data in cache
     */
    public static function store($key, $data, $ttl = self::DEFAULT_TTL) {
        $cache_key = self::CACHE_PREFIX . $key;
        
        // Log para debug
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🧪 CACHE TESTE: Armazenando chave: ' . $cache_key);
        }

        // Tentar APCu primeiro
        if (function_exists('apcu_store') && apcu_enabled()) {
            $result = apcu_store($cache_key, $data, $ttl);
            if ($result) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🧪 CACHE TESTE: APCu usado com sucesso');
                }
                return true;
            }
        }
        
        // Fallback para WordPress Transients
        $result = set_transient($cache_key, $data, $ttl);
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🧪 CACHE TESTE: Transient usado como fallback');
        }
        
        return $result;
    }

    /**
     * Get data from cache
     */
    public static function get($key) {
        $cache_key = self::CACHE_PREFIX . $key;
        
        // Tentar APCu primeiro
        if (function_exists('apcu_fetch') && apcu_enabled()) {
            $data = apcu_fetch($cache_key, $success);
            if ($success) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🧪 CACHE TESTE: Dados recuperados do APCu');
                }
                return $data;
            }
        }
        
        // Fallback para WordPress Transients
        $data = get_transient($cache_key);
        if ($data !== false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🧪 CACHE TESTE: Dados recuperados do Transient');
            }
        }
        
        return $data;
    }

    /**
     * Delete data from cache
     */
    public static function delete($key) {
        $cache_key = self::CACHE_PREFIX . $key;
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🧪 CACHE TESTE: Deletando chave: ' . $cache_key);
        }

        // Tentar APCu primeiro
        if (function_exists('apcu_delete') && apcu_enabled()) {
            apcu_delete($cache_key);
        }
        
        // Fallback para WordPress Transients
        delete_transient($cache_key);
        
        return true;
    }

    /**
     * Check if key exists in cache
     */
    public static function exists($key) {
        $cache_key = self::CACHE_PREFIX . $key;
        
        // Tentar APCu primeiro
        if (function_exists('apcu_exists') && apcu_enabled()) {
            if (apcu_exists($cache_key)) {
                return true;
            }
        }
        
        // Fallback para WordPress Transients
        return get_transient($cache_key) !== false;
    }

    /**
     * Store image data for a specific session and field
     */
    public static function store_temp_image($session_id, $field_id, $image_data) {
        $key = "image_{$session_id}_{$field_id}";
        return self::store($key, $image_data, self::SESSION_TTL);
    }

    /**
     * Get image data for a specific session and field
     */
    public static function get_temp_image($session_id, $field_id) {
        $key = "image_{$session_id}_{$field_id}";
        return self::get($key);
    }

    /**
     * Delete image data for a specific session and field
     */
    public static function delete_temp_image($session_id, $field_id) {
        $key = "image_{$session_id}_{$field_id}";
        return self::delete($key);
    }

    /**
     * Store session data (which fields have images)
     */
    public static function store_session_data($session_id, $data) {
        $key = "session_{$session_id}";
        return self::store($key, $data, self::SESSION_TTL);
    }

    /**
     * Get session data
     */
    public static function get_session_data($session_id) {
        $key = "session_{$session_id}";
        return self::get($key);
    }

    /**
     * Delete all data for a session
     */
    public static function cleanup_session($session_id) {
        // Buscar dados da sessão para saber quais imagens deletar
        $session_data = self::get_session_data($session_id);
        
        if ($session_data && isset($session_data['field_ids'])) {
            foreach ($session_data['field_ids'] as $field_id) {
                self::delete_temp_image($session_id, $field_id);
            }
        }
        
        // Deletar dados da sessão
        $key = "session_{$session_id}";
        self::delete($key);
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🧪 CACHE TESTE: Sessão limpa: ' . $session_id);
        }
    }

    /**
     * Get cache statistics for debug
     */
    public static function get_cache_info() {
        $info = array(
            'apcu_available' => function_exists('apcu_enabled') && apcu_enabled(),
            'apcu_info' => array(),
            'transient_count' => 0
        );

        if ($info['apcu_available']) {
            $info['apcu_info'] = apcu_cache_info();
        }

        return $info;
    }

    /**
     * Clean up expired cache entries (cron job)
     */
    public static function cleanup_expired_cache() {
        // APCu limpa automaticamente, mas podemos limpar transients órfãos
        global $wpdb;
        
        $expired_transients = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT option_name FROM {$wpdb->options} 
                WHERE option_name LIKE %s 
                AND option_value < %d",
                '_transient_timeout_' . self::CACHE_PREFIX . '%',
                time()
            )
        );

        foreach ($expired_transients as $transient) {
            $key = str_replace('_transient_timeout_', '', $transient->option_name);
            delete_transient($key);
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🧪 CACHE TESTE: Limpeza de cache executada. Transients expirados removidos: ' . count($expired_transients));
        }
    }
}
